#include <stdbool.h>
#include <string.h>
#include "osa.h"
#include "ql_dev.h"
#include "quec_power.h"
#include "xy_key_priv.h"
#include "xy_log.h"
#include "teldef.h"
#include "xy_led.h"
#include "xy_key.h"
#include "atChannel.h"
#include "duster.h"

/* 全局变量 */
static OSMsgQRef s_key_msg_queue = NULL;

static OSATaskRef s_scan_task = NULL;
static uint8_t s_scan_stack[KEY_SCAN_TASK_STACK_SIZE];

static OSATaskRef s_event_task = NULL;
static uint8_t s_event_stack[KEY_EVENT_TASK_STACK_SIZE];

static OSATaskRef s_pwrkey_task = NULL;
static uint8_t s_pwrkey_stack[PWRKEY_STACK_SIZE];

static const key_config_t s_key_cfgs[KEY_TYPE_MAX] = {
    [KEY_TYPE_FACTORY_RESET] = {.gpio_pin = GPIO_73, .pin_num = 129, .name = "Reset_Key"},
    [KEY_TYPE_DISPLAY] = {.gpio_pin = GPIO_23, .pin_num = 68, .name = "Display_Key"},
};

static key_ctx_t s_key_ctxs[KEY_TYPE_MAX];
static key_test_ctx_t s_test_ctx;

static const char *s_event_names[KEY_EVENT_MAX] = {
    "SHORT_PRESS", "LONG_PRESS", "DOUBLE_CLICK"};

static system_mode_e g_current_mode = SYSTEM_MODE_NORMAL;
static bool g_usb_connected = false;

static const char key_tag[] = "KEY";

/* 发送按键测试URC消息 */
static void send_key_test_urc(key_type_e key_type)
{
    char urc_msg[64];
    snprintf(urc_msg, sizeof(urc_msg), "+XYKEYTEST:%d,1", key_type);
    ATRESP(s_test_ctx.at_handle, 0, 0, urc_msg);
}

/* 检查测试超时 */
static void check_test_timeout(void)
{
    if (!s_test_ctx.is_test_mode)
    {
        return;
    }

    int kt;
    uint32_t current_tick = OSAGetTicks();
    uint32_t elapsed_ticks = current_tick - s_test_ctx.start_tick;

    if (elapsed_ticks >= s_test_ctx.timeout_ticks)
    {
        for (kt = 0; kt < KEY_TYPE_COUNT; kt++)
        {
            if (!s_test_ctx.key_pressed_flags[kt])
            {
                char timeout_urc[64];
                snprintf(timeout_urc, sizeof(timeout_urc), "+XYKEYTEST:%d,0", kt);
                ATRESP(s_test_ctx.at_handle, 0, 0, timeout_urc);
            }
        }

        s_test_ctx.is_test_mode = false;
    }
}

/* 业务函数：恢复出厂 */
static void do_factory_reset(void)
{
    XY_LOGI(key_tag, "Trigger factory reset...");

    int ret = ql_dev_restore_default();
    if (ret == 0)
    {
        XY_LOGI(key_tag, "Factory reset command sent.");
    }
    else
    {
        XY_LOGE(key_tag, "Factory reset failed, ret=%d", ret);
    }
}

/* 恢复出厂设置按键事件处理回调 */
static void factory_reset_key_event_handler(key_event_type_e et)
{
    switch (et)
    {
    case KEY_EVENT_LONG_PRESS:
        do_factory_reset();
        break;

    case KEY_EVENT_SHORT_PRESS:
        break;

    case KEY_EVENT_DOUBLE_CLICK:
        break;

    default:
        break;
    }
}

/* GPIO初始化 */
static int xy_key_gpio_init(void)
{
    int ret;

    /* 设置PIN为GPIO功能 */
    ret = ql_pin_set_func(s_key_cfgs[KEY_TYPE_FACTORY_RESET].pin_num, 1);
    if (ret != QL_GPIO_SUCCESS)
    {
        XY_LOGE(key_tag,
                "Set pin gpio func fail for %s, PIN%d, ret=%d",
                s_key_cfgs[KEY_TYPE_FACTORY_RESET].name,
                s_key_cfgs[KEY_TYPE_FACTORY_RESET].pin_num,
                ret);
        return -1;
    }

    /* 初始化GPIO */
    ret = ql_gpio_init(s_key_cfgs[KEY_TYPE_FACTORY_RESET].gpio_pin,
                       GPIO_INPUT, PULL_NONE, GPIO_HIGH);
    if (ret != QL_GPIO_SUCCESS)
    {
        XY_LOGE(key_tag,
                "Init %s fail, GPIO%d, ret=%d",
                s_key_cfgs[KEY_TYPE_FACTORY_RESET].name,
                s_key_cfgs[KEY_TYPE_FACTORY_RESET].gpio_pin,
                ret);
        return -1;
    }

    XY_LOGI(key_tag,
            "%s on GPIO%d (PIN%d)",
            s_key_cfgs[KEY_TYPE_FACTORY_RESET].name,
            s_key_cfgs[KEY_TYPE_FACTORY_RESET].gpio_pin,
            s_key_cfgs[KEY_TYPE_FACTORY_RESET].pin_num);

    return 0;
}

/* 注册回调 */
static int xy_key_register_callback(key_type_e kt, key_event_callback_t cb)
{
    if (kt >= KEY_TYPE_MAX || cb == NULL)
    {
        return -1;
    }

    s_key_ctxs[kt].cb = cb;
    return 0;
}

/* 发消息给事件任务 */
static void key_send_event(key_type_e kt, key_event_type_e ev)
{
    key_msg_t msg = {
        .key_type = kt,
        .event_type = ev,
    };

    XY_LOGI(key_tag, "Send event: %s on %s", s_event_names[ev], s_key_cfgs[kt].name);

    if (OSAMsgQSend(s_key_msg_queue, sizeof(msg), (UINT8 *)&msg, OS_NO_SUSPEND) != OS_SUCCESS)
    {
        XY_LOGE(key_tag, "MsgQ full, lose event: %s on %s", s_event_names[ev], s_key_cfgs[kt].name);
    }
}

/* 状态机 */
static void key_state_machine(key_type_e kt, ql_gpio_level_e lvl, uint32_t tick)
{
    key_ctx_t *c = &s_key_ctxs[kt];

    switch (c->state)
    {
    case KEY_STATE_IDLE:
        if (c->last_level == GPIO_HIGH && lvl == GPIO_LOW)
        {
            c->state = KEY_STATE_PRESSED;
            c->press_tick = tick;
            c->click_count = 0;

            if (s_test_ctx.is_test_mode)
            {
                send_key_test_urc(kt + 1);
                s_test_ctx.key_pressed_flags[kt + 1] = true;
            }
            else
            {
                xy_led_notify_key_pressed();
            }
        }
        break;

    case KEY_STATE_PRESSED:
        if (c->last_level == GPIO_LOW && lvl == GPIO_HIGH)
        {
            uint32_t dur = tick - c->press_tick;

            if (dur >= LONG_PRESS_TICKS)
            {
                if (!s_test_ctx.is_test_mode)
                {
                    key_send_event(kt, KEY_EVENT_LONG_PRESS);
                }
                c->state = KEY_STATE_IDLE;
            }
            else if (dur >= DEBOUNCE_TICKS)
            {
                c->click_count++;

                if (c->click_count >= 2)
                {
                    if (!s_test_ctx.is_test_mode)
                    {
                        key_send_event(kt, KEY_EVENT_DOUBLE_CLICK);
                    }
                    c->state = KEY_STATE_IDLE;
                }
                else
                {
                    c->release_tick = tick;
                    c->state = KEY_STATE_WAIT_MULTI_CLICK;
                }
            }
            else
            {
                c->state = KEY_STATE_IDLE;
            }
        }
        break;

    case KEY_STATE_WAIT_MULTI_CLICK:
        if (c->last_level == GPIO_HIGH && lvl == GPIO_LOW)
        {
            /* 第二次按下 */
            c->state = KEY_STATE_PRESSED;
            c->press_tick = tick;
        }
        else if (tick - c->release_tick >= DOUBLE_CLICK_TIMEOUT_TICKS)
        {
            /* 超时当单击 */
            if (!s_test_ctx.is_test_mode)
            {
                key_send_event(kt, KEY_EVENT_SHORT_PRESS);
            }
            c->state = KEY_STATE_IDLE;
        }
        break;
    }

    c->last_level = lvl;
}

/* 事件处理任务 */
static void key_event_task_entry(void *argv)
{
    XY_LOGI(key_tag, "Event task start");

    key_msg_t msg;
    while (1)
    {
        if (OSAMsgQRecv(s_key_msg_queue, (UINT8 *)&msg, sizeof(msg), OS_SUSPEND) == OS_SUCCESS)
        {
            key_event_callback_t cb = s_key_ctxs[msg.key_type].cb;
            if (cb)
            {
                cb(msg.event_type);
            }
        }
    }
}

/* 扫描任务 */
static void key_scan_task_entry(void *argv)
{
    int i;
    XY_LOGI(key_tag, "Scan task start");

    while (1)
    {
        uint32_t now = OSAGetTicks();

        check_test_timeout();

        for (i = 0; i < KEY_TYPE_MAX; i++)
        {
            ql_gpio_level_e lvl;
            if (ql_gpio_get_level(s_key_cfgs[i].gpio_pin, &lvl) == QL_GPIO_SUCCESS)
            {
                key_state_machine(i, lvl, now);
            }
        }

        OSATaskSleep(SCAN_PERIOD_TICKS);
    }
}

/* 释放资源 */
static void cleanup_resources(void)
{
    if (s_scan_task)
    {
        OSATaskDelete(s_scan_task);
        s_scan_task = NULL;
    }

    if (s_event_task)
    {
        OSATaskDelete(s_event_task);
        s_event_task = NULL;
    }

    if (s_key_msg_queue)
    {
        OSAMsgQDelete(s_key_msg_queue);
        s_key_msg_queue = NULL;
    }
}

/* 初始化 */
int xy_key_init(void)
{
    int i;

    if (s_key_msg_queue)
    {
        XY_LOGI(key_tag, "Already initialized");
        return 0;
    }

    XY_LOGI(key_tag, "Init key module");

    memset(s_key_ctxs, 0, sizeof(s_key_ctxs));
    memset(&s_test_ctx, 0, sizeof(s_test_ctx));

    for (i = 0; i < KEY_TYPE_MAX; i++)
    {
        s_key_ctxs[i].state = KEY_STATE_IDLE;
        s_key_ctxs[i].last_level = GPIO_HIGH;
    }

    /* 为恢复出厂设置按键注册专用的回调 */
    xy_key_register_callback(KEY_TYPE_FACTORY_RESET, factory_reset_key_event_handler);

    /* GPIO init */
    if (xy_key_gpio_init() != 0)
    {
        XY_LOGE(key_tag, "Key GPIO init failed");
        return -1;
    }

    /* 创建队列 */
    if (OSAMsgQCreate(&s_key_msg_queue, KEY_MSG_QUEUE_NAME, sizeof(key_msg_t),
                      KEY_MSG_QUEUE_SIZE, OS_FIFO) != OS_SUCCESS)
    {
        XY_LOGE(key_tag, "Create MsgQ fail");
        goto fail;
    }

    /* 创建任务 */
    if (OSATaskCreate(&s_event_task, s_event_stack, sizeof(s_event_stack),
                      KEY_EVENT_TASK_PRIORITY, KEY_EVENT_TASK_NAME,
                      key_event_task_entry, NULL) != OS_SUCCESS)
    {
        XY_LOGE(key_tag, "Create event task fail");
        goto fail;
    }

    if (OSATaskCreate(&s_scan_task, s_scan_stack, sizeof(s_scan_stack),
                      KEY_SCAN_TASK_PRIORITY, KEY_SCAN_TASK_NAME,
                      key_scan_task_entry, NULL) != OS_SUCCESS)
    {
        XY_LOGE(key_tag, "Create scan task fail");
        goto fail;
    }

    XY_LOGI(key_tag, "Key module init ok");
    return 0;

fail:
    cleanup_resources();
    return -1;
}

/* 反初始化 */
int xy_key_deinit(void)
{
    if (!s_key_msg_queue)
    {
        return 0;
    }

    cleanup_resources();
    XY_LOGI(key_tag, "Key module deinit ok");
    return 0;
}

/* 启动按键测试模式 */
int xy_key_start_test(int timeout_ms, unsigned int at_handle)
{
    int i;

    if (!s_key_msg_queue || s_test_ctx.is_test_mode)
    {
        XY_LOGE(key_tag, "Key test already in progress");
        return -1;
    }

    XY_LOGI(key_tag, "Start key test, timeout=%dms, handle=0x%x", timeout_ms, at_handle);

    s_test_ctx.is_test_mode = true;
    s_test_ctx.timeout_ticks = MS_TO_TICKS(timeout_ms);
    s_test_ctx.start_tick = OSAGetTicks();
    s_test_ctx.at_handle = at_handle;

    for (i = 0; i < KEY_TYPE_COUNT; i++)
    {
        s_test_ctx.key_pressed_flags[i] = false;
    }

    return 0;
}

#include "uapapi.h"

static int wifi_enable(bool enable)
{
    while(!GetWiFiInitStat() || UAP_STATUS() == UAP_BSS_RESET)
    {
        OSATaskSleep(100);
    }

    if (enable != (UAP_STATUS() == UAP_BSS_START))
    {
        UAP_BSS_Config(enable);
    }

    return 0;
}

static int mobile_data_enable(bool enable)
{
    while (!ATCmdSvrRdy)
    {
        OSATaskSleep(100);
    }

    int ret = 0;
    char resp_str[128] = {0};

    if (enable)
    {
        ret = SendATCMDWaitResp(AT_COMMON_EXCHANGE_CHANNEL, "AT+CFUN=1\r", 150, NULL, 1, "+CME ERROR", resp_str, sizeof(resp_str));
    }
    else
    {
        ret = SendATCMDWaitResp(AT_COMMON_EXCHANGE_CHANNEL, "AT+CFUN=0\r", 150, NULL, 1, "+CME ERROR", resp_str, sizeof(resp_str));
    }
    XY_LOGI(key_tag, "Mobile data set response: %d, %s", ret, resp_str);

    return 0;
}

static bool get_usb_connection_status(void)
{
    return PlatformVbusDetect();
}

system_mode_e xy_pwrkey_get_mode(void)
{
    return g_current_mode;
}

static int pwrkey_set_mode(system_mode_e mode)
{
    bool network_enable;
    system_mode_e previous_mode = g_current_mode;
    g_current_mode = mode;

    if (mode == SYSTEM_MODE_NORMAL)
    {
        XY_LOGI(key_tag, "Enter NORMAL mode");
        network_enable = true;

        if (xy_key_init() != 0)
        {
            XY_LOGE(key_tag, "Key module init failed");
            return -1;
        }

        if (previous_mode == SYSTEM_MODE_CHARGING)
        {
            xy_led_notify_mode_change_to_normal();
        }
    }
    else
    {
        XY_LOGI(key_tag, "Enter CHARGING mode");
        network_enable = false;

        if (xy_key_deinit() != 0)
        {
            XY_LOGE(key_tag, "Key module deinit failed");
            return -1;
        }
    }

    int wifi_ret = wifi_enable(network_enable);
    int mobile_ret = mobile_data_enable(network_enable);

    if (wifi_ret != 0 || mobile_ret != 0)
    {
        XY_LOGE(key_tag, "Network configuration failed - WiFi:%d, Mobile:%d", wifi_ret, mobile_ret);
        return -1;
    }

    return 0;
}

static void pwrkey_cb(quec_power_down_mode_e event)
{
    static bool g_pwrkey_pressed = false;

    switch (event)
    {
    case QUEC_POWER_MODE_POWER_DOWN_NORMAL:
        XY_LOGI(key_tag, "Long press shutdown trigger");

        if (!s_test_ctx.is_test_mode)
        {
            if (g_current_mode == SYSTEM_MODE_NORMAL)
            {
                if (g_usb_connected)
                {
                    pwrkey_set_mode(SYSTEM_MODE_CHARGING);
                }
                else
                {
                    ql_pwrkey_unregister_callback();
                    quec_power_control_entrance(1, QUEC_POWER_MODE_POWER_DOWN_NORMAL);
                }
            }
            else if (g_current_mode == SYSTEM_MODE_CHARGING)
            {
                pwrkey_set_mode(SYSTEM_MODE_NORMAL);
            }
        }

        break;

    case QUEC_POWER_MODE_RESET:
        XY_LOGI(key_tag, "Reset trigger");
        break;

    case QUEC_POWER_MODE_POWER_DOWN_IMMEDIATE:
        XY_LOGI(key_tag, "IMMEDIATE shutdown trigger");
        break;

    case QUEC_POWER_MODE_SHORT_PRESSED:
    {
        if (!g_pwrkey_pressed)
        {
            XY_LOGI(key_tag, "Short press trigger - key pressed");

            if (s_test_ctx.is_test_mode)
            {
                send_key_test_urc(0);
                s_test_ctx.key_pressed_flags[0] = true;
            }
            else if (g_current_mode == SYSTEM_MODE_NORMAL)
            {
                xy_led_notify_key_pressed();
            }
        }
        else
        {
            XY_LOGI(key_tag, "Short press trigger - key released");
        }
        g_pwrkey_pressed = !g_pwrkey_pressed;
    }
    break;

    default:
        XY_LOGI(key_tag, "Unknown power event: %d", event);
        break;
    }
}

static void pwrkey_task_entry(void *argv)
{
    int ret_key = 0;
    ql_pwrkey_type_e boot_type;

    ql_dev_dumpcfg_set(0);
    boot_type = ql_power_get_pwk_type();
    XY_LOGI(key_tag, "Boot type: %d", boot_type);

    if (boot_type == QUEC_PWRKEY_EXTON1)
    {
        pwrkey_set_mode(SYSTEM_MODE_NORMAL);
    }
    else if (boot_type == QUEC_PWRKEY_EXTON2)
    {
        pwrkey_set_mode(SYSTEM_MODE_CHARGING);
        g_usb_connected = true;
    }
    else
    {
        XY_LOGE(key_tag, "Unknown boot type: %d", boot_type);
    }

    ql_set_power_manage_param(QUEC_PWR_KEY_TYPE_NOR, QUEC_PWRKEY_EXTON1);
    ql_set_power_manage_param(QUEC_PWR_WAIT_RELEASE, 1);
    ql_set_power_manage_param(QUEC_PWR_PRESS_TIME, 3000);
    ql_set_power_manage_param(QUEC_PWR_PRESS_SHUTDOWN, 0);

    ret_key = ql_pwrkey_register_callback(pwrkey_cb);
    if (ret_key != 0)
    {
        XY_LOGI(key_tag, "register failed!");
    }

    while (1)
    {
        bool current_usb_status = get_usb_connection_status();

        if (current_usb_status != g_usb_connected)
        {
            g_usb_connected = current_usb_status;

            if (g_current_mode == SYSTEM_MODE_CHARGING && !g_usb_connected)
            {
                XY_LOGI(key_tag, "USB disconnected in charging mode - shutting down");
                ql_pwrkey_unregister_callback();
                quec_power_control_entrance(1, QUEC_POWER_MODE_POWER_DOWN_NORMAL);
            }
        }

        OSATaskSleep(100);
    }
}

int xy_pwrkey_init(void)
{
    if (s_pwrkey_task)
    {
        XY_LOGI(key_tag, "Already initialized");
        return 0;
    }

    if (OSATaskCreate(&s_pwrkey_task, s_pwrkey_stack,
                      PWRKEY_STACK_SIZE, PWRKEY_TASK_PRIORITY,
                      PWRKEY_TASK_NAME, pwrkey_task_entry, NULL) != OS_SUCCESS)
    {
        XY_LOGI(key_tag, "Create pwrkey task fail");
        return -1;
    }

    XY_LOGI(key_tag, "Init pwrkey module");

    return 0;
}
